# 设计文档

## 概述

TTS界面功能将为TTSDemo应用提供一个完整的文本转语音解决方案。该设计基于SwiftUI框架，使用AVFoundation的AVSpeechSynthesizer进行语音合成，并集成iOS 17+的Personal Voice功能。界面采用现代iOS设计语言，提供直观的用户体验和全面的语音控制选项。

## 架构

### 整体架构模式
- **MVVM架构**: 使用SwiftUI的声明式UI与@Observable结合
- **单一职责原则**: 每个组件负责特定功能
- **依赖注入**: 通过环境对象管理共享状态

### 核心层次结构
```
TTSInterface (主视图)
├── TextInputSection (文本输入区域)
├── PlaybackControlSection (播放控制区域)
├── VoiceSelectionSection (语音选择区域)
├── ParameterControlSection (参数控制区域)
└── PersonalVoiceSection (个人语音区域)
```

## 组件和接口

### 1. TTSManager (核心业务逻辑)
```swift
@MainActor
@Observable
class TTSManager {
    var isPlaying: Bool = false
    var currentText: String = ""
    var selectedVoice: AVSpeechSynthesisVoice?
    var speechRate: Float = 0.5
    var speechVolume: Float = 1.0
    var speechPitch: Float = 1.0
    var availableVoices: [AVSpeechSynthesisVoice] = []
    var personalVoices: [AVSpeechSynthesisVoice] = []
    var errorMessage: String?
    
    private let synthesizer = AVSpeechSynthesizer()
    private let personalVoiceManager = PersonalVoiceManager()
}
```

**主要方法:**
- `speakText(_:)`: 开始语音合成
- `stopSpeaking()`: 停止当前播放
- `loadAvailableVoices()`: 加载系统可用语音
- `saveSettings()`: 保存用户设置
- `loadSettings()`: 加载用户设置

### 2. PersonalVoiceManager (个人语音管理)
```swift
@MainActor
@Observable
class PersonalVoiceManager {
    var isPersonalVoiceAvailable: Bool = false
    var personalVoiceAuthorizationStatus: AVSpeechSynthesizer.PersonalVoiceAuthorizationStatus = .notDetermined
    var enrollmentProgress: Float = 0.0
    var isEnrolling: Bool = false
    
    func requestPersonalVoiceAuthorization() async
    func startPersonalVoiceEnrollment() async
    func checkPersonalVoiceAvailability()
}
```

### 3. UI组件接口

#### TextInputSection
```swift
struct TextInputSection: View {
    @Binding var text: String
    let placeholder: String = "输入要朗读的文本"
    let maxCharacterLimit: Int = 1000
}
```

#### PlaybackControlSection
```swift
struct PlaybackControlSection: View {
    let isPlaying: Bool
    let canPlay: Bool
    let onPlayTapped: () -> Void
    let onStopTapped: () -> Void
}
```

#### VoiceSelectionSection
```swift
struct VoiceSelectionSection: View {
    let voices: [AVSpeechSynthesisVoice]
    @Binding var selectedVoice: AVSpeechSynthesisVoice?
    let personalVoices: [AVSpeechSynthesisVoice]
}
```

#### ParameterControlSection
```swift
struct ParameterControlSection: View {
    @Binding var speechRate: Float
    @Binding var speechVolume: Float
    @Binding var speechPitch: Float
}
```

## 数据模型

### VoiceSettings (用户设置持久化)
```swift
struct VoiceSettings: Codable {
    var selectedVoiceIdentifier: String?
    var speechRate: Float
    var speechVolume: Float
    var speechPitch: Float
    var lastUsedText: String?
}
```

### VoiceDisplayModel (语音显示信息)
```swift
struct VoiceDisplayModel: Identifiable {
    let id: String
    let name: String
    let language: String
    let quality: AVSpeechSynthesisVoiceQuality
    let isPersonalVoice: Bool
    let voice: AVSpeechSynthesisVoice
}
```

## 错误处理

### 错误类型定义
```swift
enum TTSError: LocalizedError {
    case noTextProvided
    case voiceNotAvailable
    case synthesisFailure(String)
    case personalVoiceNotAuthorized
    case personalVoiceEnrollmentFailed
    case audioSessionError
    
    var errorDescription: String? {
        switch self {
        case .noTextProvided:
            return "请输入要朗读的文本"
        case .voiceNotAvailable:
            return "所选语音不可用"
        case .synthesisFailure(let message):
            return "语音合成失败: \(message)"
        case .personalVoiceNotAuthorized:
            return "个人语音功能未授权"
        case .personalVoiceEnrollmentFailed:
            return "个人语音注册失败"
        case .audioSessionError:
            return "音频会话配置失败"
        }
    }
}
```

### 错误处理策略
1. **用户友好的错误消息**: 所有错误都转换为用户可理解的中文消息
2. **优雅降级**: 当Personal Voice不可用时，回退到系统语音
3. **重试机制**: 对于临时性错误提供重试选项
4. **日志记录**: 记录详细错误信息用于调试

## 测试策略

### 单元测试
- **TTSManager测试**: 测试语音合成逻辑、设置保存/加载
- **PersonalVoiceManager测试**: 测试授权流程、可用性检查
- **数据模型测试**: 测试VoiceSettings的编码/解码

### 集成测试
- **语音播放流程**: 端到端测试文本输入到语音输出
- **设置持久化**: 测试用户设置的保存和恢复
- **错误处理**: 测试各种错误场景的处理

### UI测试
- **界面交互**: 测试按钮点击、滑块调整等用户交互
- **状态更新**: 测试UI状态与数据模型的同步
- **可访问性**: 测试VoiceOver和其他辅助功能

### 设备测试
- **不同iOS版本**: 测试iOS 17+的Personal Voice功能
- **不同设备**: 测试iPhone和iPad的界面适配
- **语音质量**: 测试不同语音的播放质量

## 性能考虑

### 内存管理
- 使用`@Observable`宏进行响应式更新
- 及时释放AVSpeechSynthesizer资源
- 避免在语音列表中保存大量语音对象

### 响应性
- 异步加载语音列表，避免阻塞UI
- 使用`@MainActor`确保UI更新在主线程
- 实现语音合成的取消机制

### 电池优化
- 在应用进入后台时暂停语音合成
- 合理配置音频会话类别
- 避免不必要的语音重新加载

## 安全和隐私

### Personal Voice隐私
- 明确说明Personal Voice数据的使用和存储
- 遵循Apple的Personal Voice使用指南
- 提供清晰的权限请求说明

### 数据保护
- 用户设置使用UserDefaults安全存储
- 不记录或传输用户输入的文本内容
- 遵循iOS数据保护最佳实践

## 可访问性

### VoiceOver支持
- 为所有UI元素提供适当的可访问性标签
- 实现自定义可访问性操作
- 确保语音参数调整对VoiceOver用户友好

### 动态类型支持
- 支持系统字体大小调整
- 确保界面在大字体下仍然可用
- 使用相对布局避免文本截断

### 色彩对比
- 确保所有文本和背景的对比度符合WCAG标准
- 支持深色模式
- 为色盲用户提供替代视觉提示