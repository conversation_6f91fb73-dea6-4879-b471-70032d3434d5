# 实现计划

- [x] 1. 创建核心数据模型和错误处理
  - 实现VoiceSettings数据模型用于设置持久化
  - 创建TTSError枚举定义所有可能的错误类型
  - 实现VoiceDisplayModel用于语音信息展示
  - _需求: 3.4, 4.6, 5.7_

- [x] 2. 实现TTSManager核心业务逻辑类
  - 创建使用@Observable宏的TTSManager类
  - 实现AVSpeechSynthesizer的初始化和配置
  - 添加语音合成的开始、停止和状态管理方法
  - 实现语音参数（语速、音量、音调）的设置功能
  - _需求: 2.1, 2.2, 2.4, 2.6, 4.2, 4.3, 4.4, 4.5_

- [x] 3. 实现语音加载和选择功能
  - 在TTSManager中添加loadAvailableVoices方法
  - 实现系统语音的异步加载和缓存
  - 添加语音选择和默认语音回退逻辑
  - 实现语音信息的格式化显示
  - _需求: 3.1, 3.2, 3.3, 3.5_

- [x] 4. 实现设置持久化功能
  - 在TTSManager中添加saveSettings和loadSettings方法
  - 使用UserDefaults存储用户的语音和参数设置
  - 实现应用启动时的设置恢复功能
  - 添加设置变更的自动保存机制
  - _需求: 3.4, 4.6_

- [x] 5. 创建PersonalVoiceManager个人语音管理类
  - 实现使用@Observable宏的PersonalVoiceManager类
  - 添加Personal Voice授权状态检查功能
  - 实现Personal Voice权限请求方法
  - 添加Personal Voice可用性检测功能
  - _需求: 5.1, 5.2, 5.6_

- [x] 6. 实现Personal Voice注册流程
  - 在PersonalVoiceManager中添加注册引导功能
  - 实现注册进度跟踪和状态更新
  - 添加注册失败的错误处理和重试机制
  - 集成Personal Voice到语音选择列表
  - _需求: 5.3, 5.4, 5.5, 5.7_

- [x] 7. 创建TextInputSection文本输入组件
  - 实现多行文本输入框的SwiftUI视图
  - 添加占位符文本"输入要朗读的文本"
  - 实现文本长度限制和滚动功能
  - 添加文本变更的实时绑定
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 8. 创建PlaybackControlSection播放控制组件
  - 实现播放/停止按钮的SwiftUI视图
  - 添加按钮状态的动态更新（播放/停止图标切换）
  - 实现基于文本内容的按钮启用/禁用逻辑
  - 添加播放和停止的回调处理
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.6_

- [x] 9. 创建VoiceSelectionSection语音选择组件
  - 实现语音选择列表的SwiftUI视图
  - 显示语音名称、语言和质量信息
  - 添加系统语音和Personal Voice的分组显示
  - 实现语音选择的绑定和状态更新
  - _需求: 3.1, 3.2, 3.3, 5.4, 5.5_

- [x] 10. 创建ParameterControlSection参数控制组件
  - 实现语速、音量、音调滑块的SwiftUI视图
  - 设置滑块的取值范围（语速0.0-1.0，音量0.0-1.0，音调0.5-2.0）
  - 添加滑块值的实时绑定和显示
  - 实现参数调整的即时预览功能
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 11. 创建PersonalVoiceSection个人语音组件
  - 实现Personal Voice设置的SwiftUI视图
  - 添加授权状态显示和权限请求按钮
  - 实现注册进度显示和引导界面
  - 添加不支持设备的提示信息
  - _需求: 5.1, 5.2, 5.3, 5.6, 5.7_

- [x] 12. 创建主界面TTSInterface视图
  - 组合所有子组件创建完整的TTS界面
  - 实现MVVM架构的数据绑定
  - 添加TTSManager和PersonalVoiceManager的环境注入
  - 设置界面布局和导航结构
  - _需求: 所有需求的UI集成_

- [x] 13. 实现错误处理和用户反馈
  - 在TTSManager中添加错误状态管理
  - 实现错误消息的用户友好显示
  - 添加语音合成失败的错误处理
  - 实现Personal Voice错误的特殊处理
  - _需求: 2.5, 5.7_

- [x] 14. 集成音频会话管理
  - 配置AVAudioSession用于语音播放
  - 实现应用生命周期的音频会话处理
  - 添加后台播放的暂停机制
  - 处理音频中断和恢复逻辑
  - _需求: 2.1, 2.4_

- [x] 15. 更新ContentView集成TTS功能
  - 将TTSInterface集成到现有的ContentView中
  - 替换"Hello, world!"占位符内容
  - 确保与现有应用结构的兼容性
  - 测试完整的用户交互流程
  - _需求: 所有需求的应用集成_

- [x] 16. 添加可访问性支持
  - 为所有UI组件添加可访问性标签
  - 实现VoiceOver的自定义操作
  - 添加动态类型支持
  - 确保色彩对比度符合标准
  - _需求: 所有需求的可访问性_

- [x] 17. 创建单元测试
  - 为TTSManager创建单元测试
  - 测试PersonalVoiceManager的功能
  - 验证数据模型的编码/解码
  - 测试错误处理逻辑
  - _需求: 所有需求的测试覆盖_

- [ ] 18. 创建UI测试
  - 测试完整的文本输入到语音播放流程
  - 验证语音选择和参数调整功能
  - 测试Personal Voice的用户交互
  - 验证错误场景的用户体验
  - _需求: 所有需求的端到端测试_