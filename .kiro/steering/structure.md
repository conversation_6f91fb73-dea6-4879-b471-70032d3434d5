# Project Structure

## Root Directory Layout
```
TTSDemo/
├── TTSDemo/                    # Main app source code
├── TTSDemoTests/              # Unit tests
├── TTSDemoUITests/            # UI tests
└── TTSDemo.xcodeproj/         # Xcode project configuration
```

## Main App Structure (`TTSDemo/`)
- **TTSDemoApp.swift** - App entry point with `@main` attribute
- **ContentView.swift** - Primary SwiftUI view containing the main UI
- **Assets.xcassets/** - Asset catalog containing:
  - App icons (`AppIcon.appiconset/`)
  - Accent colors (`AccentColor.colorset/`)
  - Other visual assets

## Testing Structure
- **TTSDemoTests/** - Contains unit test files for business logic testing
- **TTSDemoUITests/** - Contains UI automation tests for user interface testing

## Code Organization Conventions
- App entry point follows standard SwiftUI `App` protocol pattern
- Views use SwiftUI declarative syntax with `#Preview` for design-time previews
- Standard iOS app bundle structure with automatic Info.plist generation
- Asset catalogs used for all visual resources (icons, colors, images)

## File Naming Conventions
- Swift files use PascalCase matching their primary type name
- Test files append "Tests" suffix to the module they test
- Asset catalog folders use descriptive names with appropriate extensions (.colorset, .appiconset)

## Dependencies
- No external dependencies currently - uses only iOS system frameworks
- SwiftUI for UI layer
- Foundation framework for core functionality