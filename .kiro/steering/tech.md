# Technology Stack

## Framework & Language
- **Language**: Swift 5.0
- **UI Framework**: SwiftUI with SwiftUI Previews enabled
- **Platform**: iOS (iPhone and iPad support)
- **Minimum Deployment Target**: iOS 26.0
- **Xcode Version**: 26.0

## Build System
- **Build System**: Xcode project with standard iOS app structure
- **Bundle Identifier**: com.interfun.buz.TTSDemo
- **Code Signing**: Automatic
- **Asset Management**: Xcode Asset Catalogs for app icons and colors

## Swift Configuration
- Swift Approachable Concurrency enabled
- Main Actor isolation by default
- Member import visibility upcoming feature enabled
- String catalog symbol generation enabled

## Common Commands

### Building
```bash
# Build the project
xcodebuild -project TTSDemo.xcodeproj -scheme TTSDemo -configuration Debug build

# Build for release
xcodebuild -project TTSDemo.xcodeproj -scheme TTSDemo -configuration Release build
```

### Testing
```bash
# Run unit tests
xcodebuild test -project TTSDemo.xcodeproj -scheme TTSDemo -destination 'platform=iOS Simulator,name=iPhone 15'

# Run UI tests
xcodebuild test -project TTSDemo.xcodeproj -scheme TTSDemo -destination 'platform=iOS Simulator,name=iPhone 15' -only-testing:TTSDemoUITests
```

### Simulator
```bash
# Launch iOS Simulator
open -a Simulator

# Install and run on simulator
xcodebuild -project TTSDemo.xcodeproj -scheme TTSDemo -destination 'platform=iOS Simulator,name=iPhone 15' -derivedDataPath build
```