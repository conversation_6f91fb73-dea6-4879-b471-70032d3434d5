import SwiftUI

// MARK: - Accessibility Extensions
extension View {
    func ttsAccessibility(
        label: String,
        hint: String? = nil,
        value: String? = nil,
        traits: AccessibilityTraits = []
    ) -> some View {
        self
            .accessibilityLabel(label)
            .accessibilityHint(hint ?? "")
            .accessibilityValue(value ?? "")
            .accessibilityAddTraits(traits)
    }
    
    func ttsAccessibilityAction(
        named name: String,
        action: @escaping () -> Void
    ) -> some View {
        self.accessibilityAction(named: name, action)
    }
}

// MARK: - Dynamic Type Support
extension Font {
    static func ttsBody() -> Font {
        .system(.body, design: .default)
    }
    
    static func ttsHeadline() -> Font {
        .system(.headline, design: .default)
    }
    
    static func ttsCaption() -> Font {
        .system(.caption, design: .default)
    }
    
    static func ttsTitle() -> Font {
        .system(.title2, design: .default)
    }
}

// MARK: - Color Accessibility
extension Color {
    static let ttsAccent = Color.accentColor
    static let ttsPrimary = Color.primary
    static let ttsSecondary = Color.secondary
    static let ttsBackground = Color(.systemBackground)
    static let ttsGroupedBackground = Color(.systemGroupedBackground)
    
    // High contrast colors for accessibility
    static let ttsHighContrastText = Color.primary
    static let ttsHighContrastBackground = Color(.systemBackground)
    static let ttsErrorRed = Color.red
    static let ttsSuccessGreen = Color.green
    static let ttsWarningOrange = Color.orange
}

// MARK: - Accessibility Identifiers
struct TTSAccessibilityIdentifiers {
    static let textInput = "tts_text_input"
    static let playButton = "tts_play_button"
    static let stopButton = "tts_stop_button"
    static let voiceSelection = "tts_voice_selection"
    static let rateSlider = "tts_rate_slider"
    static let volumeSlider = "tts_volume_slider"
    static let pitchSlider = "tts_pitch_slider"
    static let personalVoiceSection = "tts_personal_voice_section"
    static let errorMessage = "tts_error_message"
}

// MARK: - VoiceOver Announcements
class TTSAccessibilityAnnouncer {
    static let shared = TTSAccessibilityAnnouncer()
    
    private init() {}
    
    func announce(_ message: String) {
        DispatchQueue.main.async {
            UIAccessibility.post(notification: .announcement, argument: message)
        }
    }
    
    func announceLayoutChange(_ element: Any? = nil) {
        DispatchQueue.main.async {
            UIAccessibility.post(notification: .layoutChanged, argument: element)
        }
    }
    
    func announceScreenChange(_ element: Any? = nil) {
        DispatchQueue.main.async {
            UIAccessibility.post(notification: .screenChanged, argument: element)
        }
    }
}

// MARK: - Accessibility Preferences
struct TTSAccessibilityPreferences {
    static var isVoiceOverRunning: Bool {
        UIAccessibility.isVoiceOverRunning
    }
    
    static var isReduceMotionEnabled: Bool {
        UIAccessibility.isReduceMotionEnabled
    }
    
    static var isReduceTransparencyEnabled: Bool {
        UIAccessibility.isReduceTransparencyEnabled
    }
    
    static var isBoldTextEnabled: Bool {
        UIAccessibility.isBoldTextEnabled
    }
    
    static var prefersCrossFadeTransitions: Bool {
        UIAccessibility.prefersCrossFadeTransitions
    }
}
