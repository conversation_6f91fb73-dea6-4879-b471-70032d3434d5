import Foundation
import AVFoundation
import Observation
import UIKit

@Observable
class TTSManager: NSObject {
    var isPlaying: Bool = false
    var currentText: String = ""
    var selectedVoice: AVSpeechSynthesisVoice?
    var speechRate: Float = 0.5
    var speechVolume: Float = 1.0
    var speechPitch: Float = 1.0
    var availableVoices: [AVSpeechSynthesisVoice] = []
    var personalVoices: [AVSpeechSynthesisVoice] = []
    var errorMessage: String?
    
    private let synthesizer = AVSpeechSynthesizer()
    private let personalVoiceManager = PersonalVoiceManager()
    
    override init() {
        super.init()
        synthesizer.delegate = self
        loadSettings()
        setupAppLifecycleObservers()
        Task {
            await loadAvailableVoices()
        }
    }
    
    // MARK: - App Lifecycle Management
    private func setupAppLifecycleObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    @objc private func appDidEnterBackground() {
        // Pause speech when app goes to background
        if synthesizer.isSpeaking {
            synthesizer.pauseSpeaking(at: .immediate)
        }
    }
    
    @objc private func appWillEnterForeground() {
        // Optionally resume speech when app comes to foreground
        // For now, we'll just ensure the audio session is properly configured
        do {
            try configureAudioSession()
        } catch {
            errorMessage = TTSError.audioSessionError.localizedDescription
        }
    }
    
    // MARK: - Voice Loading and Selection
    func loadAvailableVoices() async {
        let allVoices = AVSpeechSynthesisVoice.speechVoices()

        // Separate personal voices from system voices
        let systemVoices = allVoices.filter { !$0.voiceTraits.contains(.isPersonalVoice) }
        let personalVoices = allVoices.filter { $0.voiceTraits.contains(.isPersonalVoice) }

        // Sort system voices by quality and language preference
        let sortedSystemVoices = sortVoicesByQualityAndLanguage(systemVoices)

        await MainActor.run {
            self.availableVoices = sortedSystemVoices
            self.personalVoices = personalVoices

            // Set default voice if none selected
            if selectedVoice == nil {
                setDefaultVoice()
            }
        }
    }

    private func sortVoicesByQualityAndLanguage(_ voices: [AVSpeechSynthesisVoice]) -> [AVSpeechSynthesisVoice] {
        return voices.sorted { voice1, voice2 in
            // First priority: Chinese language
            let voice1IsChinese = voice1.language.hasPrefix("zh")
            let voice2IsChinese = voice2.language.hasPrefix("zh")

            if voice1IsChinese != voice2IsChinese {
                return voice1IsChinese
            }

            // Second priority: Voice quality (enhanced > premium > default)
            let quality1Priority = qualityPriority(voice1.quality)
            let quality2Priority = qualityPriority(voice2.quality)

            if quality1Priority != quality2Priority {
                return quality1Priority > quality2Priority
            }

            // Third priority: Voice name (alphabetical)
            return voice1.name < voice2.name
        }
    }

    private func qualityPriority(_ quality: AVSpeechSynthesisVoiceQuality) -> Int {
        switch quality {
        case .enhanced:
            return 3  // Highest priority
        case .premium:
            return 2  // Medium priority
        case .default:
            return 1  // Lowest priority
        @unknown default:
            return 0  // Unknown quality
        }
    }

    private func setDefaultVoice() {
        // Try to find the best quality Chinese voice first
        // Priority: Enhanced > Premium > Default quality

        // First, try to find enhanced quality Chinese voice
        if let enhancedChineseVoice = availableVoices.first(where: {
            $0.language.hasPrefix("zh") && $0.quality == .enhanced
        }) {
            selectedVoice = enhancedChineseVoice
            print("Selected enhanced quality Chinese voice: \(enhancedChineseVoice.name)")
            return
        }

        // Second, try to find premium quality Chinese voice
        if let premiumChineseVoice = availableVoices.first(where: {
            $0.language.hasPrefix("zh") && $0.quality == .premium
        }) {
            selectedVoice = premiumChineseVoice
            print("Selected premium quality Chinese voice: \(premiumChineseVoice.name)")
            return
        }

        // Third, try to find any Chinese voice (including default quality)
        if let chineseVoice = availableVoices.first(where: { $0.language.hasPrefix("zh") }) {
            selectedVoice = chineseVoice
            print("Selected Chinese voice: \(chineseVoice.name) (quality: \(chineseVoice.quality))")
            return
        }

        // Fallback to system default Chinese voice
        if let defaultVoice = AVSpeechSynthesisVoice(language: "zh-CN") {
            selectedVoice = defaultVoice
            print("Selected system default Chinese voice: \(defaultVoice.name)")
            return
        }

        // Last resort: use the first available voice (should be highest quality due to sorting)
        selectedVoice = availableVoices.first
        if let voice = selectedVoice {
            print("Selected first available voice: \(voice.name) (quality: \(voice.quality))")
        }
    }
    
    func selectVoice(_ voice: AVSpeechSynthesisVoice) {
        selectedVoice = voice
        print("Voice selected: \(voice.name) (quality: \(voice.quality), language: \(voice.language))")
        saveSettings()
    }
    
    func getVoiceDisplayModels() -> [VoiceDisplayModel] {
        let systemModels = availableVoices.map { VoiceDisplayModel(voice: $0) }
        let personalModels = personalVoices.map { VoiceDisplayModel(voice: $0) }
        return systemModels + personalModels
    }

    // MARK: - Voice Quality Information
    func getCurrentVoiceQualityInfo() -> String {
        guard let voice = selectedVoice else {
            return "未选择语音"
        }

        let qualityText = qualityDescription(voice.quality)
        return "当前语音: \(voice.name) (\(qualityText)质量)"
    }

    private func qualityDescription(_ quality: AVSpeechSynthesisVoiceQuality) -> String {
        switch quality {
        case .default:
            return "标准"
        case .enhanced:
            return "增强"
        case .premium:
            return "高级"
        @unknown default:
            return "未知"
        }
    }
    
    // MARK: - Settings Persistence
    private let settingsKey = "TTSSettings"
    
    func saveSettings() {
        let settings = VoiceSettings(
            selectedVoiceIdentifier: selectedVoice?.identifier,
            speechRate: speechRate,
            speechVolume: speechVolume,
            speechPitch: speechPitch,
            lastUsedText: currentText.isEmpty ? nil : currentText
        )
        
        if let data = try? JSONEncoder().encode(settings) {
            UserDefaults.standard.set(data, forKey: settingsKey)
        }
    }
    
    func loadSettings() {
        guard let data = UserDefaults.standard.data(forKey: settingsKey),
              let settings = try? JSONDecoder().decode(VoiceSettings.self, from: data) else {
            // Use default settings
            let defaultSettings = VoiceSettings.default
            speechRate = defaultSettings.speechRate
            speechVolume = defaultSettings.speechVolume
            speechPitch = defaultSettings.speechPitch
            return
        }
        
        speechRate = settings.speechRate
        speechVolume = settings.speechVolume
        speechPitch = settings.speechPitch
        currentText = settings.lastUsedText ?? ""
        
        // Restore selected voice (will be set after voices are loaded)
        if let voiceId = settings.selectedVoiceIdentifier {
            Task {
                await MainActor.run {
                    if let voice = AVSpeechSynthesisVoice(identifier: voiceId) {
                        selectedVoice = voice
                    }
                }
            }
        }
    }
    
    // Auto-save when parameters change
    func updateSpeechRate(_ rate: Float) {
        speechRate = rate
        saveSettings()
    }
    
    func updateSpeechVolume(_ volume: Float) {
        speechVolume = volume
        saveSettings()
    }
    
    func updateSpeechPitch(_ pitch: Float) {
        speechPitch = pitch
        saveSettings()
    }
    
    // MARK: - Speech Control Methods
    func speakText(_ text: String) {
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            errorMessage = TTSError.noTextProvided.localizedDescription
            return
        }
        
        // Stop any current speech
        if synthesizer.isSpeaking {
            synthesizer.stopSpeaking(at: .immediate)
        }
        
        // Configure audio session
        do {
            try configureAudioSession()
        } catch {
            errorMessage = TTSError.audioSessionError.localizedDescription
            return
        }
        
        // Validate selected voice
        guard let voice = selectedVoice else {
            errorMessage = TTSError.voiceNotAvailable.localizedDescription
            return
        }

        // Log voice quality information
        print("Speaking with voice: \(voice.name)")
        print("Voice quality: \(qualityDescription(voice.quality))")
        print("Voice language: \(voice.language)")
        print("Voice identifier: \(voice.identifier)")

        // Create speech utterance
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = voice
        utterance.rate = speechRate
        utterance.volume = speechVolume
        utterance.pitchMultiplier = speechPitch
        
        // Start speaking with error handling
        do {
            synthesizer.speak(utterance)
            isPlaying = true
            currentText = text
            errorMessage = nil
        } catch {
            handleSynthesisError(error)
        }
    }
    
    func stopSpeaking() {
        synthesizer.stopSpeaking(at: .immediate)
        isPlaying = false
    }
    
    // MARK: - Audio Session Configuration
    private func configureAudioSession() throws {
        let audioSession = AVAudioSession.sharedInstance()
        try audioSession.setCategory(.playback, mode: .spokenAudio, options: [.duckOthers])
        try audioSession.setActive(true)
        
        // Add notification observers for audio interruptions
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAudioInterruption),
            name: AVAudioSession.interruptionNotification,
            object: audioSession
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleRouteChange),
            name: AVAudioSession.routeChangeNotification,
            object: audioSession
        )
    }
    
    @objc private func handleAudioInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        switch type {
        case .began:
            // Audio interruption began - pause speech
            if synthesizer.isSpeaking {
                synthesizer.pauseSpeaking(at: .immediate)
            }
        case .ended:
            // Audio interruption ended - optionally resume speech
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) {
                    // Resume speech if it was paused
                    synthesizer.continueSpeaking()
                }
            }
        @unknown default:
            break
        }
    }
    
    @objc private func handleRouteChange(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }
        
        switch reason {
        case .oldDeviceUnavailable:
            // Audio device was removed (e.g., headphones unplugged)
            if synthesizer.isSpeaking {
                synthesizer.pauseSpeaking(at: .immediate)
            }
        default:
            break
        }
    }
    
    private func deactivateAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
        } catch {
            print("Failed to deactivate audio session: \(error)")
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        deactivateAudioSession()
    }
}

// MARK: - AVSpeechSynthesizerDelegate
extension TTSManager: AVSpeechSynthesizerDelegate {
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didStart utterance: AVSpeechUtterance) {
        isPlaying = true
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        isPlaying = false
        errorMessage = nil // Clear any previous errors on successful completion
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        isPlaying = false
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didPause utterance: AVSpeechUtterance) {
        isPlaying = false
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didContinue utterance: AVSpeechUtterance) {
        isPlaying = true
    }
    
    // Handle synthesis errors
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, willSpeakRangeOfSpeechString characterRange: NSRange, utterance: AVSpeechUtterance) {
        // Clear error message when speech starts successfully
        errorMessage = nil
    }
    
    // Additional error handling
    func handleSynthesisError(_ error: Error) {
        isPlaying = false
        errorMessage = TTSError.synthesisFailure(error.localizedDescription).localizedDescription
    }
    
    func clearError() {
        errorMessage = nil
    }
}
