import Foundation
import AVFoundation
import Observation
import UIKit

@Observable
class TTSManager: NSObject {
    var isPlaying: Bool = false
    var currentText: String = ""
    var selectedVoice: AVSpeechSynthesisVoice?
    var speechRate: Float = 0.5
    var speechVolume: Float = 1.0
    var speechPitch: Float = 1.0
    var availableVoices: [AVSpeechSynthesisVoice] = []
    var personalVoices: [AVSpeechSynthesisVoice] = []
    var errorMessage: String?
    
    private let synthesizer = AVSpeechSynthesizer()
    private let personalVoiceManager = PersonalVoiceManager()
    
    override init() {
        super.init()
        synthesizer.delegate = self
        loadSettings()
        setupAppLifecycleObservers()
        Task {
            await loadAvailableVoices()
        }
    }
    
    // MARK: - App Lifecycle Management
    private func setupAppLifecycleObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    @objc private func appDidEnterBackground() {
        // Pause speech when app goes to background
        if synthesizer.isSpeaking {
            synthesizer.pauseSpeaking(at: .immediate)
        }
    }
    
    @objc private func appWillEnterForeground() {
        // Optionally resume speech when app comes to foreground
        // For now, we'll just ensure the audio session is properly configured
        do {
            try configureAudioSession()
        } catch {
            errorMessage = TTSError.audioSessionError.localizedDescription
        }
    }
    
    // MARK: - Voice Loading and Selection
    func loadAvailableVoices() async {
        let allVoices = AVSpeechSynthesisVoice.speechVoices()
        
        // Separate personal voices from system voices
        let systemVoices = allVoices.filter { !$0.voiceTraits.contains(.isPersonalVoice) }
        let personalVoices = allVoices.filter { $0.voiceTraits.contains(.isPersonalVoice) }
        
        await MainActor.run {
            self.availableVoices = systemVoices
            self.personalVoices = personalVoices
            
            // Set default voice if none selected
            if selectedVoice == nil {
                setDefaultVoice()
            }
        }
    }
    
    private func setDefaultVoice() {
        // Try to find a Chinese voice first, then fallback to default
        if let chineseVoice = availableVoices.first(where: { $0.language.hasPrefix("zh") }) {
            selectedVoice = chineseVoice
        } else if let defaultVoice = AVSpeechSynthesisVoice(language: "zh-CN") {
            selectedVoice = defaultVoice
        } else {
            selectedVoice = availableVoices.first
        }
    }
    
    func selectVoice(_ voice: AVSpeechSynthesisVoice) {
        selectedVoice = voice
        saveSettings()
    }
    
    func getVoiceDisplayModels() -> [VoiceDisplayModel] {
        let systemModels = availableVoices.map { VoiceDisplayModel(voice: $0) }
        let personalModels = personalVoices.map { VoiceDisplayModel(voice: $0) }
        return systemModels + personalModels
    }
    
    // MARK: - Settings Persistence
    private let settingsKey = "TTSSettings"
    
    func saveSettings() {
        let settings = VoiceSettings(
            selectedVoiceIdentifier: selectedVoice?.identifier,
            speechRate: speechRate,
            speechVolume: speechVolume,
            speechPitch: speechPitch,
            lastUsedText: currentText.isEmpty ? nil : currentText
        )
        
        if let data = try? JSONEncoder().encode(settings) {
            UserDefaults.standard.set(data, forKey: settingsKey)
        }
    }
    
    func loadSettings() {
        guard let data = UserDefaults.standard.data(forKey: settingsKey),
              let settings = try? JSONDecoder().decode(VoiceSettings.self, from: data) else {
            // Use default settings
            let defaultSettings = VoiceSettings.default
            speechRate = defaultSettings.speechRate
            speechVolume = defaultSettings.speechVolume
            speechPitch = defaultSettings.speechPitch
            return
        }
        
        speechRate = settings.speechRate
        speechVolume = settings.speechVolume
        speechPitch = settings.speechPitch
        currentText = settings.lastUsedText ?? ""
        
        // Restore selected voice (will be set after voices are loaded)
        if let voiceId = settings.selectedVoiceIdentifier {
            Task {
                await MainActor.run {
                    if let voice = AVSpeechSynthesisVoice(identifier: voiceId) {
                        selectedVoice = voice
                    }
                }
            }
        }
    }
    
    // Auto-save when parameters change
    func updateSpeechRate(_ rate: Float) {
        speechRate = rate
        saveSettings()
    }
    
    func updateSpeechVolume(_ volume: Float) {
        speechVolume = volume
        saveSettings()
    }
    
    func updateSpeechPitch(_ pitch: Float) {
        speechPitch = pitch
        saveSettings()
    }
    
    // MARK: - Speech Control Methods
    func speakText(_ text: String) {
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            errorMessage = TTSError.noTextProvided.localizedDescription
            return
        }
        
        // Stop any current speech
        if synthesizer.isSpeaking {
            synthesizer.stopSpeaking(at: .immediate)
        }
        
        // Configure audio session
        do {
            try configureAudioSession()
        } catch {
            errorMessage = TTSError.audioSessionError.localizedDescription
            return
        }
        
        // Validate selected voice
        guard let voice = selectedVoice else {
            errorMessage = TTSError.voiceNotAvailable.localizedDescription
            return
        }
        
        // Create speech utterance
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = voice
        utterance.rate = speechRate
        utterance.volume = speechVolume
        utterance.pitchMultiplier = speechPitch
        
        // Start speaking with error handling
        do {
            synthesizer.speak(utterance)
            isPlaying = true
            currentText = text
            errorMessage = nil
        } catch {
            handleSynthesisError(error)
        }
    }
    
    func stopSpeaking() {
        synthesizer.stopSpeaking(at: .immediate)
        isPlaying = false
    }
    
    // MARK: - Audio Session Configuration
    private func configureAudioSession() throws {
        let audioSession = AVAudioSession.sharedInstance()
        try audioSession.setCategory(.playback, mode: .spokenAudio, options: [.duckOthers])
        try audioSession.setActive(true)
        
        // Add notification observers for audio interruptions
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAudioInterruption),
            name: AVAudioSession.interruptionNotification,
            object: audioSession
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleRouteChange),
            name: AVAudioSession.routeChangeNotification,
            object: audioSession
        )
    }
    
    @objc private func handleAudioInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        switch type {
        case .began:
            // Audio interruption began - pause speech
            if synthesizer.isSpeaking {
                synthesizer.pauseSpeaking(at: .immediate)
            }
        case .ended:
            // Audio interruption ended - optionally resume speech
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) {
                    // Resume speech if it was paused
                    synthesizer.continueSpeaking()
                }
            }
        @unknown default:
            break
        }
    }
    
    @objc private func handleRouteChange(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }
        
        switch reason {
        case .oldDeviceUnavailable:
            // Audio device was removed (e.g., headphones unplugged)
            if synthesizer.isSpeaking {
                synthesizer.pauseSpeaking(at: .immediate)
            }
        default:
            break
        }
    }
    
    private func deactivateAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
        } catch {
            print("Failed to deactivate audio session: \(error)")
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        deactivateAudioSession()
    }
}

// MARK: - AVSpeechSynthesizerDelegate
extension TTSManager: AVSpeechSynthesizerDelegate {
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didStart utterance: AVSpeechUtterance) {
        isPlaying = true
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        isPlaying = false
        errorMessage = nil // Clear any previous errors on successful completion
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        isPlaying = false
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didPause utterance: AVSpeechUtterance) {
        isPlaying = false
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didContinue utterance: AVSpeechUtterance) {
        isPlaying = true
    }
    
    // Handle synthesis errors
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, willSpeakRangeOfSpeechString characterRange: NSRange, utterance: AVSpeechUtterance) {
        // Clear error message when speech starts successfully
        errorMessage = nil
    }
    
    // Additional error handling
    func handleSynthesisError(_ error: Error) {
        isPlaying = false
        errorMessage = TTSError.synthesisFailure(error.localizedDescription).localizedDescription
    }
    
    func clearError() {
        errorMessage = nil
    }
}
