import SwiftUI

struct PlaybackControlSection: View {
    let isPlaying: Bool
    let canPlay: Bool
    let onPlayTapped: () -> Void
    let onStopTapped: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Text("播放控制")
                .font(.headline)
                .foregroundColor(.primary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: 20) {
                // Play/Stop Button
                Button(action: {
                    if isPlaying {
                        onStopTapped()
                    } else {
                        onPlayTapped()
                    }
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: isPlaying ? "stop.fill" : "play.fill")
                            .font(.title2)
                        
                        Text(isPlaying ? "停止" : "播放")
                            .font(.headline)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(canPlay ? (isPlaying ? Color.red : Color.blue) : Color.gray)
                    )
                }
                .disabled(!canPlay && !isPlaying)
                .accessibilityLabel(isPlaying ? "停止播放" : "开始播放")
                .accessibilityHint(canPlay ? "点击以\(isPlaying ? "停止" : "开始")语音播放" : "请先输入文本")
                
                Spacer()
                
                // Status Indicator
                HStack(spacing: 8) {
                    Circle()
                        .fill(isPlaying ? Color.green : Color.gray)
                        .frame(width: 8, height: 8)
                        .scaleEffect(isPlaying ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.6).repeatForever(autoreverses: true), value: isPlaying)
                    
                    Text(isPlaying ? "正在播放" : "已停止")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.vertical, 8)
    }
}

#Preview {
    VStack(spacing: 20) {
        PlaybackControlSection(
            isPlaying: false,
            canPlay: true,
            onPlayTapped: { print("Play tapped") },
            onStopTapped: { print("Stop tapped") }
        )
        
        PlaybackControlSection(
            isPlaying: true,
            canPlay: true,
            onPlayTapped: { print("Play tapped") },
            onStopTapped: { print("Stop tapped") }
        )
        
        PlaybackControlSection(
            isPlaying: false,
            canPlay: false,
            onPlayTapped: { print("Play tapped") },
            onStopTapped: { print("Stop tapped") }
        )
    }
    .padding()
}