import SwiftUI

struct TextInputSection: View {
    @Binding var text: String
    let placeholder: String = "输入要朗读的文本"
    let maxCharacterLimit: Int = 1000
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("文本输入")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("\(text.count)/\(maxCharacterLimit)")
                    .font(.caption)
                    .foregroundColor(text.count > maxCharacterLimit ? .red : .secondary)
            }
            
            TextEditor(text: $text)
                .frame(minHeight: 120)
                .padding(12)
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
                .overlay(
                    // Placeholder text
                    Group {
                        if text.isEmpty {
                            VStack {
                                HStack {
                                    Text(placeholder)
                                        .foregroundColor(.secondary)
                                        .padding(.leading, 16)
                                        .padding(.top, 20)
                                    Spacer()
                                }
                                Spacer()
                            }
                        }
                    }
                )
                .onChange(of: text) { _, newValue in
                    // Limit text length
                    if newValue.count > maxCharacterLimit {
                        text = String(newValue.prefix(maxCharacterLimit))
                    }
                }
            
            if text.count > maxCharacterLimit {
                Text("文本长度超出限制")
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("文本输入区域")
        .accessibilityHint("输入要转换为语音的文本，最多\(maxCharacterLimit)个字符")
    }
}

#Preview {
    @State var sampleText = ""
    return TextInputSection(text: $sampleText)
        .padding()
}