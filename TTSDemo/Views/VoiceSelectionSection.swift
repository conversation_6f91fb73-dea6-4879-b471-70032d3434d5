import SwiftUI
import AVFoundation

struct VoiceSelectionSection: View {
    let voices: [AVSpeechSynthesisVoice]
    @Binding var selectedVoice: AVSpeechSynthesisVoice?
    let personalVoices: [AVSpeechSynthesisVoice]
    let onVoiceSelected: (AVSpeechSynthesisVoice) -> Void
    
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("语音选择")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                }) {
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.blue)
                        .font(.caption)
                }
            }
            
            // Current Selection Display
            if let currentVoice = selectedVoice {
                VoiceRowView(
                    voice: currentVoice,
                    isSelected: true,
                    isPersonalVoice: personalVoices.contains(where: { $0.identifier == currentVoice.identifier })
                )
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            }
            
            if isExpanded {
                ScrollView {
                    LazyVStack(spacing: 8) {
                        // Personal Voices Section
                        if !personalVoices.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("个人语音")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.purple)
                                    .padding(.horizontal, 4)
                                
                                ForEach(personalVoices, id: \.identifier) { voice in
                                    VoiceRowView(
                                        voice: voice,
                                        isSelected: selectedVoice?.identifier == voice.identifier,
                                        isPersonalVoice: true
                                    )
                                    .onTapGesture {
                                        selectedVoice = voice
                                        onVoiceSelected(voice)
                                        withAnimation {
                                            isExpanded = false
                                        }
                                    }
                                }
                            }
                            
                            Divider()
                                .padding(.vertical, 8)
                        }
                        
                        // System Voices Section
                        VStack(alignment: .leading, spacing: 8) {
                            Text("系统语音")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.blue)
                                .padding(.horizontal, 4)
                            
                            ForEach(voices, id: \.identifier) { voice in
                                VoiceRowView(
                                    voice: voice,
                                    isSelected: selectedVoice?.identifier == voice.identifier,
                                    isPersonalVoice: false
                                )
                                .onTapGesture {
                                    selectedVoice = voice
                                    onVoiceSelected(voice)
                                    withAnimation {
                                        isExpanded = false
                                    }
                                }
                            }
                        }
                    }
                }
                .frame(maxHeight: 300)
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("语音选择")
        .accessibilityHint("选择用于语音合成的语音")
    }
}

struct VoiceRowView: View {
    let voice: AVSpeechSynthesisVoice
    let isSelected: Bool
    let isPersonalVoice: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            // Selection Indicator
            Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                .foregroundColor(isSelected ? .blue : .gray)
                .font(.title3)
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(voice.name)
                        .font(.body)
                        .fontWeight(isSelected ? .semibold : .regular)
                        .foregroundColor(.primary)
                    
                    if isPersonalVoice {
                        Image(systemName: "person.fill")
                            .foregroundColor(.purple)
                            .font(.caption)
                    }
                }
                
                HStack(spacing: 8) {
                    Text(voice.language)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("•")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(qualityDescription(voice.quality))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(isSelected ? Color.blue.opacity(0.1) : Color.clear)
        .cornerRadius(8)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(voice.name), \(voice.language), \(qualityDescription(voice.quality))")
        .accessibilityHint(isSelected ? "当前选中的语音" : "点击选择此语音")
    }
    
    private func qualityDescription(_ quality: AVSpeechSynthesisVoiceQuality) -> String {
        switch quality {
        case .default:
            return "标准"
        case .enhanced:
            return "增强"
        case .premium:
            return "高级"
        @unknown default:
            return "未知"
        }
    }
}

#Preview {
    @State var selectedVoice: AVSpeechSynthesisVoice? = AVSpeechSynthesisVoice.speechVoices().first
    
    return VoiceSelectionSection(
        voices: Array(AVSpeechSynthesisVoice.speechVoices().prefix(5)),
        selectedVoice: $selectedVoice,
        personalVoices: [],
        onVoiceSelected: { voice in
            print("Selected voice: \(voice.name)")
        }
    )
    .padding()
}