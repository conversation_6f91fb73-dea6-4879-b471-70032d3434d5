import SwiftUI

struct ParameterControlSection: View {
    @Binding var speechRate: Float
    @Binding var speechVolume: Float
    @Binding var speechPitch: Float
    
    let onRateChanged: (Float) -> Void
    let onVolumeChanged: (Float) -> Void
    let onPitchChanged: (Float) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("语音参数")
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(spacing: 20) {
                // Speech Rate Control
                ParameterSliderView(
                    title: "语速",
                    value: $speechRate,
                    range: 0.0...1.0,
                    step: 0.05,
                    unit: "",
                    icon: "speedometer",
                    color: .blue,
                    formatValue: { value in
                        String(format: "%.2f", value)
                    },
                    onValueChanged: onRateChanged
                )
                
                // Speech Volume Control
                ParameterSliderView(
                    title: "音量",
                    value: $speechVolume,
                    range: 0.0...1.0,
                    step: 0.05,
                    unit: "",
                    icon: "speaker.wave.2",
                    color: .green,
                    formatValue: { value in
                        String(format: "%.2f", value)
                    },
                    onValueChanged: onVolumeChanged
                )
                
                // Speech Pitch Control
                ParameterSliderView(
                    title: "音调",
                    value: $speechPitch,
                    range: 0.5...2.0,
                    step: 0.05,
                    unit: "",
                    icon: "tuningfork",
                    color: .orange,
                    formatValue: { value in
                        String(format: "%.2f", value)
                    },
                    onValueChanged: onPitchChanged
                )
            }
            .padding(.vertical, 8)
        }
    }
}

struct ParameterSliderView: View {
    let title: String
    @Binding var value: Float
    let range: ClosedRange<Float>
    let step: Float
    let unit: String
    let icon: String
    let color: Color
    let formatValue: (Float) -> String
    let onValueChanged: (Float) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                HStack(spacing: 8) {
                    Image(systemName: icon)
                        .foregroundColor(color)
                        .font(.body)
                    
                    Text(title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                }
                
                Spacer()
                
                Text("\(formatValue(value))\(unit)")
                    .font(.body)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
                    .monospacedDigit()
            }
            
            HStack(spacing: 12) {
                Text(formatValue(range.lowerBound))
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .monospacedDigit()
                
                Slider(
                    value: Binding(
                        get: { value },
                        set: { newValue in
                            let steppedValue = round(newValue / step) * step
                            value = steppedValue
                            onValueChanged(steppedValue)
                        }
                    ),
                    in: range
                ) {
                    Text(title)
                } minimumValueLabel: {
                    EmptyView()
                } maximumValueLabel: {
                    EmptyView()
                }
                .accentColor(color)
                
                Text(formatValue(range.upperBound))
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .monospacedDigit()
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title)滑块")
        .accessibilityValue("\(formatValue(value))\(unit)")
        .accessibilityHint("滑动以调整\(title)，范围从\(formatValue(range.lowerBound))到\(formatValue(range.upperBound))")
    }
}

#Preview {
    @State var rate: Float = 0.5
    @State var volume: Float = 1.0
    @State var pitch: Float = 1.0
    
    return ParameterControlSection(
        speechRate: $rate,
        speechVolume: $volume,
        speechPitch: $pitch,
        onRateChanged: { value in print("Rate: \(value)") },
        onVolumeChanged: { value in print("Volume: \(value)") },
        onPitchChanged: { value in print("Pitch: \(value)") }
    )
    .padding()
}