import Foundation
import AVFoundation

// 语音质量测试工具
class VoiceQualityTest {
    
    static func testVoiceQualityPriority() {
        print("=== 语音质量测试 ===")
        
        let allVoices = AVSpeechSynthesisVoice.speechVoices()
        let systemVoices = allVoices.filter { !$0.voiceTraits.contains(.isPersonalVoice) }
        
        print("\n1. 所有可用的中文语音:")
        let chineseVoices = systemVoices.filter { $0.language.hasPrefix("zh") }
        
        for voice in chineseVoices {
            let qualityText = qualityDescription(voice.quality)
            print("   - \(voice.name) (\(voice.language)) - \(qualityText)质量")
        }
        
        print("\n2. 按质量分组的中文语音:")
        
        let enhancedVoices = chineseVoices.filter { $0.quality == .enhanced }
        let premiumVoices = chineseVoices.filter { $0.quality == .premium }
        let defaultVoices = chineseVoices.filter { $0.quality == .default }
        
        if !enhancedVoices.isEmpty {
            print("   增强质量语音 (\(enhancedVoices.count)个):")
            for voice in enhancedVoices {
                print("     - \(voice.name) (\(voice.language))")
            }
        }
        
        if !premiumVoices.isEmpty {
            print("   高级质量语音 (\(premiumVoices.count)个):")
            for voice in premiumVoices {
                print("     - \(voice.name) (\(voice.language))")
            }
        }
        
        if !defaultVoices.isEmpty {
            print("   标准质量语音 (\(defaultVoices.count)个):")
            for voice in defaultVoices {
                print("     - \(voice.name) (\(voice.language))")
            }
        }
        
        print("\n3. 推荐的默认语音选择:")
        
        // 模拟新的语音选择逻辑
        if let enhancedVoice = enhancedVoices.first {
            print("   ✅ 选择增强质量语音: \(enhancedVoice.name)")
        } else if let premiumVoice = premiumVoices.first {
            print("   ✅ 选择高级质量语音: \(premiumVoice.name)")
        } else if let defaultVoice = defaultVoices.first {
            print("   ⚠️  只能选择标准质量语音: \(defaultVoice.name)")
        } else {
            print("   ❌ 没有找到中文语音")
        }
        
        print("\n4. 语音质量统计:")
        print("   - 增强质量: \(enhancedVoices.count)个")
        print("   - 高级质量: \(premiumVoices.count)个")
        print("   - 标准质量: \(defaultVoices.count)个")
        print("   - 总计中文语音: \(chineseVoices.count)个")
        
        print("\n=== 测试完成 ===")
    }
    
    private static func qualityDescription(_ quality: AVSpeechSynthesisVoiceQuality) -> String {
        switch quality {
        case .default:
            return "标准"
        case .enhanced:
            return "增强"
        case .premium:
            return "高级"
        @unknown default:
            return "未知"
        }
    }
}
