import Foundation
import AVFoundation

// MARK: - Voice Settings for Persistence
struct VoiceSettings: Codable {
    var selectedVoiceIdentifier: String?
    var speechRate: Float
    var speechVolume: Float
    var speechPitch: Float
    var lastUsedText: String?
    
    static let `default` = VoiceSettings(
        selectedVoiceIdentifier: nil,
        speechRate: 0.5,
        speechVolume: 1.0,
        speechPitch: 1.0,
        lastUsedText: nil
    )
}

// MARK: - Voice Display Model
struct VoiceDisplayModel: Identifiable {
    let id: String
    let name: String
    let language: String
    let quality: AVSpeechSynthesisVoiceQuality
    let isPersonalVoice: Bool
    let voice: AVSpeechSynthesisVoice
    
    init(voice: AVSpeechSynthesisVoice) {
        self.voice = voice
        self.id = voice.identifier
        self.name = voice.name
        self.language = voice.language
        self.quality = voice.quality
        self.isPersonalVoice = voice.voiceTraits.contains(.isPersonalVoice)
    }
}

// MARK: - TTS Error Types
enum TTSError: LocalizedError {
    case noTextProvided
    case voiceNotAvailable
    case synthesisFailure(String)
    case personalVoiceNotAuthorized
    case personalVoiceEnrollmentFailed
    case audioSessionError
    
    var errorDescription: String? {
        switch self {
        case .noTextProvided:
            return "请输入要朗读的文本"
        case .voiceNotAvailable:
            return "所选语音不可用"
        case .synthesisFailure(let message):
            return "语音合成失败: \(message)"
        case .personalVoiceNotAuthorized:
            return "个人语音功能未授权"
        case .personalVoiceEnrollmentFailed:
            return "个人语音注册失败"
        case .audioSessionError:
            return "音频会话配置失败"
        }
    }
}