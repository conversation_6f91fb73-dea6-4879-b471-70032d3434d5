import Foundation
import UIKit
import AVFoundation
import Observation

@MainActor
@Observable
class PersonalVoiceManager {
    var isPersonalVoiceAvailable: Bool = false
    var personalVoiceAuthorizationStatus: AVSpeechSynthesizer.PersonalVoiceAuthorizationStatus = .notDetermined
    var enrollmentProgress: Float = 0.0
    var isEnrolling: Bool = false
    
    init() {
        // 初始化时不立即检查系统权限状态，让用户主动触发权限申请
        // 只检查设备是否支持个人语音功能
        if #available(iOS 17.0, *) {
            // 保持默认的 .notDetermined 状态，直到用户主动申请权限
            personalVoiceAuthorizationStatus = .notDetermined
        } else {
            personalVoiceAuthorizationStatus = .unsupported
        }

        // 设置应用生命周期监听
        setupAppLifecycleObservers()
    }

    // MARK: - App Lifecycle Management
    private func setupAppLifecycleObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
    }

    @objc private func appWillEnterForeground() {
        print("PersonalVoiceManager: App will enter foreground, refreshing authorization status")
        Task { @MainActor in
            refreshAuthorizationStatus()
        }
    }

    @objc private func appDidBecomeActive() {
        print("PersonalVoiceManager: App did become active, refreshing authorization status")
        Task { @MainActor in
            refreshAuthorizationStatus()
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Personal Voice Authorization
    func requestPersonalVoiceAuthorization() async {
        let status = await AVSpeechSynthesizer.requestPersonalVoiceAuthorization()
        await MainActor.run {
            self.personalVoiceAuthorizationStatus = status
            self.checkPersonalVoiceAvailability()
        }
    }
    
    func checkPersonalVoiceAvailability() {
        if #available(iOS 17.0, *) {
            // 只更新可用性状态，不覆盖授权状态
            // 授权状态应该只通过 requestPersonalVoiceAuthorization 或明确的权限检查来更新
            isPersonalVoiceAvailable = personalVoiceAuthorizationStatus == .authorized
        } else {
            personalVoiceAuthorizationStatus = .unsupported
            isPersonalVoiceAvailable = false
        }
    }

    // 显式检查系统当前的权限状态（仅在需要时调用）
    func refreshAuthorizationStatus() {
        if #available(iOS 17.0, *) {
            let currentStatus = AVSpeechSynthesizer.personalVoiceAuthorizationStatus
            print("PersonalVoiceManager: Current system authorization status: \(currentStatus)")

            // 只有当状态真正发生变化时才更新
            if personalVoiceAuthorizationStatus != currentStatus {
                print("PersonalVoiceManager: Authorization status changed from \(personalVoiceAuthorizationStatus) to \(currentStatus)")
                personalVoiceAuthorizationStatus = currentStatus
            }

            isPersonalVoiceAvailable = personalVoiceAuthorizationStatus == .authorized
            print("PersonalVoiceManager: Personal voice available: \(isPersonalVoiceAvailable)")
        } else {
            personalVoiceAuthorizationStatus = .unsupported
            isPersonalVoiceAvailable = false
        }
    }
    
    // MARK: - Device Support Check
    var isPersonalVoiceSupported: Bool {
        if #available(iOS 17.0, *) {
            return true
        } else {
            return false
        }
    }
    
    var authorizationStatusDescription: String {
        switch personalVoiceAuthorizationStatus {
        case .notDetermined:
            return "需要申请录音权限"
        case .denied:
            return "录音权限被拒绝"
        case .unsupported:
            return "设备不支持个人语音"
        case .authorized:
            return "录音权限已获得"
        @unknown default:
            return "未知状态"
        }
    }
    
    // MARK: - Personal Voice Enrollment
    func startPersonalVoiceEnrollment() async {
        guard personalVoiceAuthorizationStatus == .authorized else {
            enrollmentError = TTSError.personalVoiceNotAuthorized.localizedDescription
            return
        }
        
        await MainActor.run {
            isEnrolling = true
            enrollmentProgress = 0.0
            enrollmentError = nil
        }
        
        // 引导用户到系统设置进行个人语音注册
        await MainActor.run {
            openPersonalVoiceSettings()
        }
        
        // 开始监控个人语音注册状态
        await monitorEnrollmentProgress()
    }
    
    private func openPersonalVoiceSettings() {
        // 尝试打开个人语音设置页面
        if let settingsUrl = URL(string: "App-prefs:ACCESSIBILITY&path=PERSONAL_VOICE") {
            UIApplication.shared.open(settingsUrl) { success in
                if !success {
                    // 如果无法直接打开个人语音设置，打开辅助功能设置
                    if let accessibilityUrl = URL(string: "App-prefs:ACCESSIBILITY") {
                        UIApplication.shared.open(accessibilityUrl)
                    } else {
                        // 最后回退到通用设置
                        if let generalUrl = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(generalUrl)
                        }
                    }
                }
            }
        }
    }
    
    private func monitorEnrollmentProgress() async {
        // 监控个人语音注册进度
        let maxWaitTime = 300 // 5分钟超时
        var elapsedTime = 0
        
        while isEnrolling && elapsedTime < maxWaitTime {
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒检查一次
            elapsedTime += 2
            
            await MainActor.run {
                // 更新进度显示
                enrollmentProgress = min(Float(elapsedTime) / Float(maxWaitTime), 0.9)
                
                // 检查是否有新的个人语音
                let currentVoices = getPersonalVoices()
                if !currentVoices.isEmpty {
                    // 发现个人语音，注册成功
                    isEnrolling = false
                    enrollmentProgress = 1.0
                    checkPersonalVoiceAvailability()
                    return
                }
            }
        }
        
        // 超时或用户取消
        await MainActor.run {
            if isEnrolling {
                isEnrolling = false
                enrollmentProgress = 0.0
                enrollmentError = "注册超时或被取消，请重试"
            }
        }
    }
    
    // MARK: - Error Handling
    var enrollmentError: String?
    
    func handleEnrollmentError(_ error: Error) {
        isEnrolling = false
        enrollmentProgress = 0.0
        enrollmentError = TTSError.personalVoiceEnrollmentFailed.localizedDescription
        print("Personal Voice enrollment error: \(error.localizedDescription)")
    }
    
    func clearEnrollmentError() {
        enrollmentError = nil
    }
    
    func cancelEnrollment() {
        isEnrolling = false
        enrollmentProgress = 0.0
        enrollmentError = nil
    }
    
    // MARK: - Helper Methods
    func getPersonalVoices() -> [AVSpeechSynthesisVoice] {
        return AVSpeechSynthesisVoice.speechVoices().filter { $0.voiceTraits.contains(.isPersonalVoice) }
    }
    
    var hasPersonalVoices: Bool {
        return !getPersonalVoices().isEmpty
    }
    
    var canEnrollPersonalVoice: Bool {
        return isPersonalVoiceSupported && personalVoiceAuthorizationStatus == .authorized && !isEnrolling
    }
}
