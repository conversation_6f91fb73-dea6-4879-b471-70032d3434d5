// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		04B5822F2E387E17001D7B7F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 04B582192E387E16001D7B7F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 04B582202E387E16001D7B7F;
			remoteInfo = TTSDemo;
		};
		04B582392E387E17001D7B7F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 04B582192E387E16001D7B7F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 04B582202E387E16001D7B7F;
			remoteInfo = TTSDemo;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		04B582212E387E16001D7B7F /* TTSDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TTSDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		04B5822E2E387E17001D7B7F /* TTSDemoTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TTSDemoTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		04B582382E387E17001D7B7F /* TTSDemoUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TTSDemoUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		04B582232E387E16001D7B7F /* TTSDemo */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TTSDemo;
			sourceTree = "<group>";
		};
		04B582312E387E17001D7B7F /* TTSDemoTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TTSDemoTests;
			sourceTree = "<group>";
		};
		04B5823B2E387E17001D7B7F /* TTSDemoUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TTSDemoUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		04B5821E2E387E16001D7B7F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04B5822B2E387E17001D7B7F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04B582352E387E17001D7B7F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		04B582182E387E16001D7B7F = {
			isa = PBXGroup;
			children = (
				04B582232E387E16001D7B7F /* TTSDemo */,
				04B582312E387E17001D7B7F /* TTSDemoTests */,
				04B5823B2E387E17001D7B7F /* TTSDemoUITests */,
				04B582222E387E16001D7B7F /* Products */,
			);
			sourceTree = "<group>";
		};
		04B582222E387E16001D7B7F /* Products */ = {
			isa = PBXGroup;
			children = (
				04B582212E387E16001D7B7F /* TTSDemo.app */,
				04B5822E2E387E17001D7B7F /* TTSDemoTests.xctest */,
				04B582382E387E17001D7B7F /* TTSDemoUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		04B582202E387E16001D7B7F /* TTSDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04B582422E387E17001D7B7F /* Build configuration list for PBXNativeTarget "TTSDemo" */;
			buildPhases = (
				04B5821D2E387E16001D7B7F /* Sources */,
				04B5821E2E387E16001D7B7F /* Frameworks */,
				04B5821F2E387E16001D7B7F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				04B582232E387E16001D7B7F /* TTSDemo */,
			);
			name = TTSDemo;
			packageProductDependencies = (
			);
			productName = TTSDemo;
			productReference = 04B582212E387E16001D7B7F /* TTSDemo.app */;
			productType = "com.apple.product-type.application";
		};
		04B5822D2E387E17001D7B7F /* TTSDemoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04B582452E387E17001D7B7F /* Build configuration list for PBXNativeTarget "TTSDemoTests" */;
			buildPhases = (
				04B5822A2E387E17001D7B7F /* Sources */,
				04B5822B2E387E17001D7B7F /* Frameworks */,
				04B5822C2E387E17001D7B7F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				04B582302E387E17001D7B7F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				04B582312E387E17001D7B7F /* TTSDemoTests */,
			);
			name = TTSDemoTests;
			packageProductDependencies = (
			);
			productName = TTSDemoTests;
			productReference = 04B5822E2E387E17001D7B7F /* TTSDemoTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		04B582372E387E17001D7B7F /* TTSDemoUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04B582482E387E17001D7B7F /* Build configuration list for PBXNativeTarget "TTSDemoUITests" */;
			buildPhases = (
				04B582342E387E17001D7B7F /* Sources */,
				04B582352E387E17001D7B7F /* Frameworks */,
				04B582362E387E17001D7B7F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				04B5823A2E387E17001D7B7F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				04B5823B2E387E17001D7B7F /* TTSDemoUITests */,
			);
			name = TTSDemoUITests;
			packageProductDependencies = (
			);
			productName = TTSDemoUITests;
			productReference = 04B582382E387E17001D7B7F /* TTSDemoUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		04B582192E387E16001D7B7F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 2600;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					04B582202E387E16001D7B7F = {
						CreatedOnToolsVersion = 26.0;
					};
					04B5822D2E387E17001D7B7F = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = 04B582202E387E16001D7B7F;
					};
					04B582372E387E17001D7B7F = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = 04B582202E387E16001D7B7F;
					};
				};
			};
			buildConfigurationList = 04B5821C2E387E16001D7B7F /* Build configuration list for PBXProject "TTSDemo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 04B582182E387E16001D7B7F;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 04B582222E387E16001D7B7F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				04B582202E387E16001D7B7F /* TTSDemo */,
				04B5822D2E387E17001D7B7F /* TTSDemoTests */,
				04B582372E387E17001D7B7F /* TTSDemoUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		04B5821F2E387E16001D7B7F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04B5822C2E387E17001D7B7F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04B582362E387E17001D7B7F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		04B5821D2E387E16001D7B7F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04B5822A2E387E17001D7B7F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04B582342E387E17001D7B7F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		04B582302E387E17001D7B7F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 04B582202E387E16001D7B7F /* TTSDemo */;
			targetProxy = 04B5822F2E387E17001D7B7F /* PBXContainerItemProxy */;
		};
		04B5823A2E387E17001D7B7F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 04B582202E387E16001D7B7F /* TTSDemo */;
			targetProxy = 04B582392E387E17001D7B7F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		04B582402E387E17001D7B7F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		04B582412E387E17001D7B7F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		04B582432E387E17001D7B7F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = HQUMU47JDU;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "mic useage";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = recognnn;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.interfun.buz;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = buz_dev_20250701;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		04B582442E387E17001D7B7F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = HQUMU47JDU;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "mic useage";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = recognnn;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.interfun.buz;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = buz_dev_20250701;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		04B582462E387E17001D7B7F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.interfun.buz.TTSDemoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TTSDemo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TTSDemo";
			};
			name = Debug;
		};
		04B582472E387E17001D7B7F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.interfun.buz.TTSDemoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TTSDemo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TTSDemo";
			};
			name = Release;
		};
		04B582492E387E17001D7B7F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.interfun.buz.TTSDemoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = TTSDemo;
			};
			name = Debug;
		};
		04B5824A2E387E17001D7B7F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.interfun.buz.TTSDemoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = TTSDemo;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		04B5821C2E387E16001D7B7F /* Build configuration list for PBXProject "TTSDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04B582402E387E17001D7B7F /* Debug */,
				04B582412E387E17001D7B7F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04B582422E387E17001D7B7F /* Build configuration list for PBXNativeTarget "TTSDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04B582432E387E17001D7B7F /* Debug */,
				04B582442E387E17001D7B7F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04B582452E387E17001D7B7F /* Build configuration list for PBXNativeTarget "TTSDemoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04B582462E387E17001D7B7F /* Debug */,
				04B582472E387E17001D7B7F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04B582482E387E17001D7B7F /* Build configuration list for PBXNativeTarget "TTSDemoUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04B582492E387E17001D7B7F /* Debug */,
				04B5824A2E387E17001D7B7F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 04B582192E387E16001D7B7F /* Project object */;
}
