import XCTest
import AVFoundation
@testable import TTSDemo

@MainActor
final class TTSManagerTests: XCTestCase {
    var ttsManager: TTSManager!
    
    override func setUp() {
        super.setUp()
        ttsManager = TTSManager()
    }
    
    override func tearDown() {
        ttsManager = nil
        super.tearDown()
    }
    
    func testInitialState() {
        XCTAssertFalse(ttsManager.isPlaying)
        XCTAssertEqual(ttsManager.currentText, "")
        XCTAssertEqual(ttsManager.speechRate, 0.5)
        XCTAssertEqual(ttsManager.speechVolume, 1.0)
        XCTAssertEqual(ttsManager.speechPitch, 1.0)
        XCTAssertNil(ttsManager.errorMessage)
    }
    
    func testSpeechParameterUpdates() {
        // Test speech rate update
        ttsManager.updateSpeechRate(0.8)
        XCTAssertEqual(ttsManager.speechRate, 0.8)
        
        // Test speech volume update
        ttsManager.updateSpeechVolume(0.7)
        XCTAssertEqual(ttsManager.speechVolume, 0.7)
        
        // Test speech pitch update
        ttsManager.updateSpeechPitch(1.5)
        XCTAssertEqual(ttsManager.speechPitch, 1.5)
    }
    
    func testEmptyTextError() {
        ttsManager.speakText("")
        XCTAssertNotNil(ttsManager.errorMessage)
        XCTAssertEqual(ttsManager.errorMessage, TTSError.noTextProvided.localizedDescription)
        XCTAssertFalse(ttsManager.isPlaying)
    }
    
    func testWhitespaceOnlyTextError() {
        ttsManager.speakText("   \n\t   ")
        XCTAssertNotNil(ttsManager.errorMessage)
        XCTAssertEqual(ttsManager.errorMessage, TTSError.noTextProvided.localizedDescription)
        XCTAssertFalse(ttsManager.isPlaying)
    }
    
    func testVoiceSelection() async {
        // Wait for voices to load
        await ttsManager.loadAvailableVoices()
        
        XCTAssertFalse(ttsManager.availableVoices.isEmpty)
        
        if let firstVoice = ttsManager.availableVoices.first {
            ttsManager.selectVoice(firstVoice)
            XCTAssertEqual(ttsManager.selectedVoice?.identifier, firstVoice.identifier)
        }
    }
    
    func testVoiceDisplayModels() async {
        await ttsManager.loadAvailableVoices()
        
        let displayModels = ttsManager.getVoiceDisplayModels()
        XCTAssertFalse(displayModels.isEmpty)
        
        for model in displayModels {
            XCTAssertFalse(model.id.isEmpty)
            XCTAssertFalse(model.name.isEmpty)
            XCTAssertFalse(model.language.isEmpty)
        }
    }
    
    func testErrorClearing() {
        ttsManager.speakText("")
        XCTAssertNotNil(ttsManager.errorMessage)

        ttsManager.clearError()
        XCTAssertNil(ttsManager.errorMessage)
    }

    // MARK: - Voice Quality Tests
    func testVoiceQualityPriority() async {
        await ttsManager.loadAvailableVoices()

        // Test that voices are sorted by quality
        let chineseVoices = ttsManager.availableVoices.filter { $0.language.hasPrefix("zh") }

        if chineseVoices.count > 1 {
            // Check if enhanced quality voices come before default quality voices
            let enhancedVoices = chineseVoices.filter { $0.quality == .enhanced }
            let defaultVoices = chineseVoices.filter { $0.quality == .default }

            if !enhancedVoices.isEmpty && !defaultVoices.isEmpty {
                let firstEnhancedIndex = chineseVoices.firstIndex { $0.quality == .enhanced }
                let firstDefaultIndex = chineseVoices.firstIndex { $0.quality == .default }

                if let enhancedIdx = firstEnhancedIndex, let defaultIdx = firstDefaultIndex {
                    XCTAssertLessThan(enhancedIdx, defaultIdx, "Enhanced quality voices should come before default quality voices")
                }
            }
        }
    }

    func testDefaultVoiceQualitySelection() async {
        await ttsManager.loadAvailableVoices()

        // Test that the default voice selection prioritizes quality
        let chineseVoices = ttsManager.availableVoices.filter { $0.language.hasPrefix("zh") }

        if !chineseVoices.isEmpty {
            XCTAssertNotNil(ttsManager.selectedVoice, "A default voice should be selected")

            if let selectedVoice = ttsManager.selectedVoice {
                XCTAssertTrue(selectedVoice.language.hasPrefix("zh"), "Default voice should be Chinese")

                // If enhanced quality voices are available, the selected voice should be enhanced
                let enhancedVoices = chineseVoices.filter { $0.quality == .enhanced }
                if !enhancedVoices.isEmpty {
                    XCTAssertEqual(selectedVoice.quality, .enhanced, "Should select enhanced quality voice when available")
                }
            }
        }
    }

    func testVoiceQualityInfo() async {
        await ttsManager.loadAvailableVoices()

        let qualityInfo = ttsManager.getCurrentVoiceQualityInfo()
        XCTAssertFalse(qualityInfo.isEmpty, "Voice quality info should not be empty")

        if ttsManager.selectedVoice != nil {
            XCTAssertTrue(qualityInfo.contains("当前语音"), "Quality info should contain voice information")
        }
    }

    func testVoiceSelectionLogging() async {
        await ttsManager.loadAvailableVoices()

        if let firstVoice = ttsManager.availableVoices.first {
            // This test verifies that voice selection includes quality logging
            ttsManager.selectVoice(firstVoice)
            XCTAssertEqual(ttsManager.selectedVoice?.identifier, firstVoice.identifier)
        }
    }
}