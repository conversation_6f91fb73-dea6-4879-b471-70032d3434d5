import XCTest
import AVFoundation
@testable import TTSDemo

@MainActor
final class TTSManagerTests: XCTestCase {
    var ttsManager: TTSManager!
    
    override func setUp() {
        super.setUp()
        ttsManager = TTSManager()
    }
    
    override func tearDown() {
        ttsManager = nil
        super.tearDown()
    }
    
    func testInitialState() {
        XCTAssertFalse(ttsManager.isPlaying)
        XCTAssertEqual(ttsManager.currentText, "")
        XCTAssertEqual(ttsManager.speechRate, 0.5)
        XCTAssertEqual(ttsManager.speechVolume, 1.0)
        XCTAssertEqual(ttsManager.speechPitch, 1.0)
        XCTAssertNil(ttsManager.errorMessage)
    }
    
    func testSpeechParameterUpdates() {
        // Test speech rate update
        ttsManager.updateSpeechRate(0.8)
        XCTAssertEqual(ttsManager.speechRate, 0.8)
        
        // Test speech volume update
        ttsManager.updateSpeechVolume(0.7)
        XCTAssertEqual(ttsManager.speechVolume, 0.7)
        
        // Test speech pitch update
        ttsManager.updateSpeechPitch(1.5)
        XCTAssertEqual(ttsManager.speechPitch, 1.5)
    }
    
    func testEmptyTextError() {
        ttsManager.speakText("")
        XCTAssertNotNil(ttsManager.errorMessage)
        XCTAssertEqual(ttsManager.errorMessage, TTSError.noTextProvided.localizedDescription)
        XCTAssertFalse(ttsManager.isPlaying)
    }
    
    func testWhitespaceOnlyTextError() {
        ttsManager.speakText("   \n\t   ")
        XCTAssertNotNil(ttsManager.errorMessage)
        XCTAssertEqual(ttsManager.errorMessage, TTSError.noTextProvided.localizedDescription)
        XCTAssertFalse(ttsManager.isPlaying)
    }
    
    func testVoiceSelection() async {
        // Wait for voices to load
        await ttsManager.loadAvailableVoices()
        
        XCTAssertFalse(ttsManager.availableVoices.isEmpty)
        
        if let firstVoice = ttsManager.availableVoices.first {
            ttsManager.selectVoice(firstVoice)
            XCTAssertEqual(ttsManager.selectedVoice?.identifier, firstVoice.identifier)
        }
    }
    
    func testVoiceDisplayModels() async {
        await ttsManager.loadAvailableVoices()
        
        let displayModels = ttsManager.getVoiceDisplayModels()
        XCTAssertFalse(displayModels.isEmpty)
        
        for model in displayModels {
            XCTAssertFalse(model.id.isEmpty)
            XCTAssertFalse(model.name.isEmpty)
            XCTAssertFalse(model.language.isEmpty)
        }
    }
    
    func testErrorClearing() {
        ttsManager.speakText("")
        XCTAssertNotNil(ttsManager.errorMessage)
        
        ttsManager.clearError()
        XCTAssertNil(ttsManager.errorMessage)
    }
}