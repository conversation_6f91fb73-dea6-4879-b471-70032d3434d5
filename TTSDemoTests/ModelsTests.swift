import XCTest
import AVFoundation
@testable import TTSDemo

final class ModelsTests: XCTestCase {
    
    func testVoiceSettingsDefaultValues() {
        let defaultSettings = VoiceSettings.default
        
        XCTAssertNil(defaultSettings.selectedVoiceIdentifier)
        XCTAssertEqual(defaultSettings.speechRate, 0.5)
        XCTAssertEqual(defaultSettings.speechVolume, 1.0)
        XCTAssertEqual(defaultSettings.speechPitch, 1.0)
        XCTAssertNil(defaultSettings.lastUsedText)
    }
    
    func testVoiceSettingsEncoding() throws {
        let settings = VoiceSettings(
            selectedVoiceIdentifier: "test.voice.id",
            speechRate: 0.7,
            speechVolume: 0.8,
            speechPitch: 1.2,
            lastUsedText: "Test text"
        )
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(settings)
        XCTAssertFalse(data.isEmpty)
        
        let decoder = JSONDecoder()
        let decodedSettings = try decoder.decode(VoiceSettings.self, from: data)
        
        XCTAssertEqual(decodedSettings.selectedVoiceIdentifier, settings.selectedVoiceIdentifier)
        XCTAssertEqual(decodedSettings.speechRate, settings.speechRate)
        XCTAssertEqual(decodedSettings.speechVolume, settings.speechVolume)
        XCTAssertEqual(decodedSettings.speechPitch, settings.speechPitch)
        XCTAssertEqual(decodedSettings.lastUsedText, settings.lastUsedText)
    }
    
    func testVoiceDisplayModelCreation() {
        let voices = AVSpeechSynthesisVoice.speechVoices()
        guard let testVoice = voices.first else {
            XCTFail("No voices available for testing")
            return
        }
        
        let displayModel = VoiceDisplayModel(voice: testVoice)
        
        XCTAssertEqual(displayModel.id, testVoice.identifier)
        XCTAssertEqual(displayModel.name, testVoice.name)
        XCTAssertEqual(displayModel.language, testVoice.language)
        XCTAssertEqual(displayModel.quality, testVoice.quality)
        XCTAssertEqual(displayModel.voice.identifier, testVoice.identifier)
        XCTAssertEqual(displayModel.isPersonalVoice, testVoice.voiceTraits.contains(.isPersonalVoice))
    }
    
    func testTTSErrorDescriptions() {
        let errors: [TTSError] = [
            .noTextProvided,
            .voiceNotAvailable,
            .synthesisFailure("Test failure"),
            .personalVoiceNotAuthorized,
            .personalVoiceEnrollmentFailed,
            .audioSessionError
        ]
        
        for error in errors {
            XCTAssertNotNil(error.errorDescription)
            XCTAssertFalse(error.errorDescription!.isEmpty)
        }
        
        // Test specific error message
        let synthesisError = TTSError.synthesisFailure("Custom error message")
        XCTAssertTrue(synthesisError.errorDescription!.contains("Custom error message"))
    }
    
    func testTTSErrorLocalization() {
        let noTextError = TTSError.noTextProvided
        XCTAssertEqual(noTextError.errorDescription, "请输入要朗读的文本")
        
        let voiceNotAvailableError = TTSError.voiceNotAvailable
        XCTAssertEqual(voiceNotAvailableError.errorDescription, "所选语音不可用")
        
        let personalVoiceError = TTSError.personalVoiceNotAuthorized
        XCTAssertEqual(personalVoiceError.errorDescription, "个人语音功能未授权")
    }
}