# 个人语音授权状态显示问题修复报告

## 问题描述

应用在用户尚未主动申请个人语音权限的情况下，错误地显示"录音权限被拒绝"状态，而不是正确的"需要申请录音权限"状态。

## 问题根因分析

1. **初始化时的权限检查**：`PersonalVoiceManager.swift` 的 `init()` 方法中直接调用了 `AVSpeechSynthesizer.personalVoiceAuthorizationStatus`，这会在应用启动时立即获取系统的当前授权状态。

2. **界面加载时的自动检查**：`TTSInterface.swift` 的 `onAppear` 方法中调用了 `checkPersonalVoiceAvailability()`，进一步加剧了问题。

3. **测试期望不一致**：测试文件中的期望状态描述与实际实现不匹配。

## 修复方案

### 1. 修改 PersonalVoiceManager.swift

**修复前：**
```swift
init() {
    if #available(iOS 17.0, *) {
        personalVoiceAuthorizationStatus = AVSpeechSynthesizer.personalVoiceAuthorizationStatus
    } else {
        personalVoiceAuthorizationStatus = .unsupported
    }
}
```

**修复后：**
```swift
init() {
    // 初始化时不立即检查系统权限状态，让用户主动触发权限申请
    // 只检查设备是否支持个人语音功能
    if #available(iOS 17.0, *) {
        // 保持默认的 .notDetermined 状态，直到用户主动申请权限
        personalVoiceAuthorizationStatus = .notDetermined
    } else {
        personalVoiceAuthorizationStatus = .unsupported
    }
}
```

### 2. 修改权限检查逻辑

**新增方法：**
```swift
// 显式检查系统当前的权限状态（仅在需要时调用）
func refreshAuthorizationStatus() {
    if #available(iOS 17.0, *) {
        personalVoiceAuthorizationStatus = AVSpeechSynthesizer.personalVoiceAuthorizationStatus
        isPersonalVoiceAvailable = personalVoiceAuthorizationStatus == .authorized
    } else {
        personalVoiceAuthorizationStatus = .unsupported
        isPersonalVoiceAvailable = false
    }
}
```

**修改现有方法：**
```swift
func checkPersonalVoiceAvailability() {
    if #available(iOS 17.0, *) {
        // 只更新可用性状态，不覆盖授权状态
        // 授权状态应该只通过 requestPersonalVoiceAuthorization 或明确的权限检查来更新
        isPersonalVoiceAvailable = personalVoiceAuthorizationStatus == .authorized
    } else {
        personalVoiceAuthorizationStatus = .unsupported
        isPersonalVoiceAvailable = false
    }
}
```

### 3. 修改 TTSInterface.swift

**修复前：**
```swift
.onAppear {
    Task {
        await ttsManager.loadAvailableVoices()
    }
    
    // Check personal voice availability
    personalVoiceManager.checkPersonalVoiceAvailability()
    
    TTSAccessibilityAnnouncer.shared.announceScreenChange()
}
```

**修复后：**
```swift
.onAppear {
    Task {
        await ttsManager.loadAvailableVoices()
    }
    
    // 不在应用启动时自动检查权限状态，让用户主动申请
    // personalVoiceManager.checkPersonalVoiceAvailability()
    
    TTSAccessibilityAnnouncer.shared.announceScreenChange()
}
```

### 4. 修复测试文件

**修复前：**
```swift
let descriptions = [
    AVSpeechSynthesizer.PersonalVoiceAuthorizationStatus.notDetermined: "未确定",
    .denied: "已拒绝",
    .unsupported: "不支持",
    .authorized: "已授权"
]
```

**修复后：**
```swift
let descriptions = [
    AVSpeechSynthesizer.PersonalVoiceAuthorizationStatus.notDetermined: "需要申请录音权限",
    .denied: "录音权限被拒绝",
    .unsupported: "设备不支持个人语音",
    .authorized: "录音权限已获得"
]
```

## 修复效果

1. **应用启动时**：现在会正确显示"需要申请录音权限"状态，而不是错误的"录音权限被拒绝"。

2. **用户体验改善**：用户不会在未申请权限时看到令人困惑的拒绝状态。

3. **权限申请流程**：只有在用户主动点击"申请录音权限"按钮时，才会触发实际的权限检查和申请。

4. **状态一致性**：UI显示的状态与实际的授权状态保持一致。

## 验证方法

1. 启动应用，检查个人语音部分是否显示"需要申请录音权限"
2. 点击"申请录音权限"按钮，验证权限申请流程
3. 运行单元测试，确保状态描述测试通过
4. 在不同权限状态下验证UI显示的正确性

## 注意事项

- 修复后，应用不会在启动时自动检查系统权限状态
- 如果需要在特定场景下检查当前权限状态，可以调用新增的 `refreshAuthorizationStatus()` 方法
- 权限状态的更新主要通过用户主动的权限申请操作来触发
