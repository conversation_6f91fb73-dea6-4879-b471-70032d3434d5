//
//  TTSDemoUITests.swift
//  TTSDemoUITests
//
//  Created by jack on 2025/7/29.
//

import XCTest

final class TTSDemoUITests: XCTestCase {
    var app: XCUIApplication!

    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launch()
    }

    override func tearDownWithError() throws {
        app = nil
    }

    @MainActor
    func testTTSInterfaceElements() throws {
        // Test that main UI elements are present
        XCTAssertTrue(app.navigationBars["文本转语音"].exists)
        
        // Test text input section
        let textInputSection = app.staticTexts["文本输入"]
        XCTAssertTrue(textInputSection.exists)
        
        // Test playback control section
        let playbackSection = app.staticTexts["播放控制"]
        XCTAssertTrue(playbackSection.exists)
        
        // Test voice selection section
        let voiceSection = app.staticTexts["语音选择"]
        XCTAssertTrue(voiceSection.exists)
        
        // Test parameter control section
        let parameterSection = app.staticTexts["语音参数"]
        XCTAssertTrue(parameterSection.exists)
        
        // Test personal voice section
        let personalVoiceSection = app.staticTexts["个人语音"]
        XCTAssertTrue(personalVoiceSection.exists)
    }
    
    @MainActor
    func testTextInputAndPlayback() throws {
        // Find and tap the text editor
        let textEditor = app.textViews.firstMatch
        XCTAssertTrue(textEditor.exists)
        
        // Enter test text
        textEditor.tap()
        textEditor.typeText("这是一个测试文本")
        
        // Find the play button
        let playButton = app.buttons.containing(.staticText, identifier: "播放").firstMatch
        XCTAssertTrue(playButton.exists)
        XCTAssertTrue(playButton.isEnabled)
        
        // Tap play button
        playButton.tap()
        
        // Wait a moment for the button state to change
        let expectation = XCTestExpectation(description: "Wait for button state change")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 2.0)
        
        // Check if stop button appears (button text should change)
        let stopButton = app.buttons.containing(.staticText, identifier: "停止").firstMatch
        if stopButton.exists {
            stopButton.tap()
        }
    }
    
    @MainActor
    func testVoiceSelection() throws {
        // Find voice selection section
        let voiceSection = app.staticTexts["语音选择"]
        XCTAssertTrue(voiceSection.exists)
        
        // Look for expand button (chevron)
        let expandButton = app.buttons.matching(identifier: "chevron.down").firstMatch
        if expandButton.exists {
            expandButton.tap()
            
            // Wait for voice list to appear
            let expectation = XCTestExpectation(description: "Wait for voice list")
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                expectation.fulfill()
            }
            wait(for: [expectation], timeout: 1.0)
            
            // Check if system voices section appears
            let systemVoicesLabel = app.staticTexts["系统语音"]
            XCTAssertTrue(systemVoicesLabel.exists)
        }
    }
    
    @MainActor
    func testParameterSliders() throws {
        // Test speech rate slider
        let rateSlider = app.sliders.containing(.staticText, identifier: "语速").firstMatch
        if rateSlider.exists {
            // Adjust the slider
            rateSlider.adjust(toNormalizedSliderPosition: 0.7)
        }
        
        // Test volume slider
        let volumeSlider = app.sliders.containing(.staticText, identifier: "音量").firstMatch
        if volumeSlider.exists {
            volumeSlider.adjust(toNormalizedSliderPosition: 0.8)
        }
        
        // Test pitch slider
        let pitchSlider = app.sliders.containing(.staticText, identifier: "音调").firstMatch
        if pitchSlider.exists {
            pitchSlider.adjust(toNormalizedSliderPosition: 0.6)
        }
    }
    
    @MainActor
    func testPersonalVoiceSection() throws {
        // Scroll to personal voice section if needed
        let personalVoiceSection = app.staticTexts["个人语音"]
        if !personalVoiceSection.isHittable {
            app.scrollViews.firstMatch.swipeUp()
        }
        
        XCTAssertTrue(personalVoiceSection.exists)
        
        // Check authorization status display
        let authStatusText = app.staticTexts["授权状态"]
        XCTAssertTrue(authStatusText.exists)
        
        // Look for authorization request button if present
        let requestAuthButton = app.buttons["请求授权"]
        if requestAuthButton.exists {
            XCTAssertTrue(requestAuthButton.isEnabled)
        }
    }
    
    @MainActor
    func testErrorHandling() throws {
        // Test empty text error
        let playButton = app.buttons.containing(.staticText, identifier: "播放").firstMatch
        XCTAssertTrue(playButton.exists)
        
        // Play button should be disabled when no text is entered
        XCTAssertFalse(playButton.isEnabled)
        
        // Enter some text to enable the button
        let textEditor = app.textViews.firstMatch
        textEditor.tap()
        textEditor.typeText("测试")
        
        // Now the play button should be enabled
        XCTAssertTrue(playButton.isEnabled)
    }
    
    @MainActor
    func testAccessibility() throws {
        // Test that key elements have accessibility labels
        let textEditor = app.textViews.firstMatch
        XCTAssertTrue(textEditor.exists)
        
        let playButton = app.buttons.containing(.staticText, identifier: "播放").firstMatch
        XCTAssertTrue(playButton.exists)
        
        // Test VoiceOver navigation
        if UIAccessibility.isVoiceOverRunning {
            // Additional VoiceOver specific tests could go here
            XCTAssertTrue(textEditor.isAccessibilityElement)
            XCTAssertTrue(playButton.isAccessibilityElement)
        }
    }

    @MainActor
    func testLaunchPerformance() throws {
        // This measures how long it takes to launch your application.
        measure(metrics: [XCTApplicationLaunchMetric()]) {
            XCUIApplication().launch()
        }
    }
}